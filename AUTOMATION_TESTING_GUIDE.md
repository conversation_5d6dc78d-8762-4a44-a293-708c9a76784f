# 🧪 Automation Manager Testing & Debugging Guide

## 🔍 **Issues Identified & Solutions Implemented**

### ✅ **Issue 1: Email Not Being Sent**
**Root Cause**: No automation execution logic was implemented
**Solution**: Created complete automation execution engine

### ✅ **Issue 2: Data Persistence Issues in Edit Form**
**Root Cause**: Automation schema didn't properly store new trigger/action configurations
**Solution**: Updated schema and data loading logic

### ✅ **Issue 3: User Attribution Problems**
**Root Cause**: Virtual field population issues
**Solution**: Fixed virtual field definitions and population queries

---

## 🧪 **Step-by-Step Testing Process**

### **Phase 1: Test Edit Form Data Loading**

1. **Navigate to Automation Manager**
   ```
   http://localhost:3000/automation
   ```

2. **Click "Edit" on your existing automation**
   - ✅ **Expected**: All form fields should be populated with saved data
   - ❌ **If empty**: Check browser console for errors

3. **Verify User Attribution**
   - ✅ **Expected**: "Created by" and "Last modified by" should show actual user names
   - ❌ **If "Unknown"**: Check server logs for population errors

### **Phase 2: Test Automation Creation**

1. **Create a new automation with these settings**:
   ```
   Name: Test Email Automation
   Description: Testing email automation
   Trigger: Project status moved
   Status: prospect
   Action: Email
   Email Subject: Test: Hello [customer_name]
   Email Body: Dear [customer_name], your project is now a prospect. Contact [project_lead] for details.
   Email Addresses: <EMAIL>
   ```

2. **Save the automation**
   - ✅ **Expected**: Success message and redirect to automation list
   - ❌ **If error**: Check for template validation errors

### **Phase 3: Test Automation Execution**

#### **Method 1: Manual Project Status Change**

1. **Find a project with status "lead"**
   ```
   http://localhost:3000/project
   ```

2. **Change project status to "prospect"**
   - Edit the project
   - Change status from "lead" to "prospect"
   - Save the project

3. **Check server logs**
   ```
   Look for messages like:
   - "Found X active automations for account..."
   - "Email sent for automation to: <EMAIL>"
   - Any error messages
   ```

4. **Check your email**
   - ✅ **Expected**: Email with processed template variables
   - ❌ **If no email**: Check server logs for errors

#### **Method 2: API Testing (Recommended)**

1. **Use the test endpoint**:
   ```bash
   curl -X POST http://localhost:3000/api/automation/test \
     -H "Content-Type: application/json" \
     -d '{
       "projectId": "YOUR_PROJECT_ID",
       "fromStatus": "lead",
       "toStatus": "prospect"
     }'
   ```

2. **Get debug information**:
   ```bash
   curl "http://localhost:3000/api/automation/test?projectId=YOUR_PROJECT_ID"
   ```

#### **Method 3: Node.js Test Script**

1. **Update test-automation.js with your IDs**
2. **Run the test**:
   ```bash
   node test-automation.js
   ```

---

## 🔧 **Debugging Checklist**

### **If Emails Are Not Sent**

1. **Check automation is active**
   - Go to automation list
   - Verify "Active" status is enabled

2. **Check trigger configuration**
   - Verify trigger type is "project_status_moved"
   - Verify target status matches the status you're changing to

3. **Check server logs for**:
   ```
   ✅ "Found X active automations for account..."
   ✅ "Email sent for automation to: ..."
   ❌ "No email recipients found for automation"
   ❌ "No valid email addresses found after template processing"
   ❌ Error messages in automation processing
   ```

4. **Check email configuration**:
   - Verify email addresses are properly formatted
   - Check if template variables are valid
   - Ensure email service is configured

### **If Edit Form Data Is Empty**

1. **Check browser console** for JavaScript errors
2. **Check server logs** for database/population errors
3. **Verify automation exists** in database
4. **Check schema compatibility** - old automations might need migration

### **If User Attribution Shows "Unknown"**

1. **Check user references** in automation documents
2. **Verify population queries** are working
3. **Check user collection** has proper data

### **If Template Variables Don't Work**

1. **Check project has customer data**:
   ```javascript
   // In browser console on project page:
   console.log('Project data:', project);
   console.log('Customer data:', project.documents?.customer);
   ```

2. **Verify template variable syntax**:
   ```
   ✅ [customer_name]
   ✅ [customer_email]
   ✅ [customer_phone]
   ✅ [project_lead]
   ❌ [customer-name] (wrong syntax)
   ❌ {customer_name} (wrong brackets)
   ```

---

## 📊 **Expected Server Log Output**

When automation triggers successfully, you should see:
```
Found 1 active automations for account 507f1f77bcf86cd799439011
Processing automation: Test Email Automation
Template variables extracted: {
  customer_name: "John Doe",
  customer_email: "<EMAIL>",
  customer_phone: "+**********",
  project_lead: "Jane Smith"
}
Email sent for automation to: <EMAIL>
```

---

## 🚨 **Common Issues & Solutions**

### **Issue**: "Module not found: Can't resolve './process-automations'"
**Solution**: Restart the development server (`rs` in terminal)

### **Issue**: "Invalid template variables in email subject: [invalid_var]"
**Solution**: Use only valid variables: `[customer_name]`, `[customer_email]`, `[customer_phone]`, `[project_lead]`

### **Issue**: "No email recipients found for automation"
**Solution**: Ensure email addresses field is not empty and contains valid email addresses

### **Issue**: Template variables not replaced
**Solution**: 
1. Check project has associated customer and assigned user
2. Verify customer has email/phone data
3. Check project has assignedTo user

---

## 📧 **Email Testing Tips**

1. **Use a real email address** you can access
2. **Check spam folder** if email doesn't arrive
3. **Test with simple template** first (no variables)
4. **Add multiple recipients** separated by commas
5. **Use template variables** to verify data processing

---

## 🎯 **Success Criteria**

✅ Edit form loads with all saved data
✅ User attribution shows actual user names  
✅ New automations save successfully
✅ Template variable validation works
✅ Project status changes trigger automations
✅ Emails are sent with processed template variables
✅ Server logs show automation processing details
✅ No JavaScript or server errors
