// Test script to manually trigger automations
// Run this with: node test-automation.js

// REPLACE THESE WITH YOUR ACTUAL IDs
const projectId = '67e6f2c11c7eac73087cc900'; // Replace with your actual project ID
const automationId = '6840b1460ba0b71d97af21b2'; // Replace with your actual automation ID

// You can get these IDs by:
// 1. Go to http://localhost:3000/project and copy a project ID from the URL
// 2. Go to http://localhost:3000/automation and copy an automation ID from the URL

async function testAutomation() {
  try {
    console.log('🧪 Testing Automation System...\n');

    // First, get debug info about the project and automations
    console.log('📊 Getting debug info...');
    const debugResponse = await fetch(`http://localhost:3000/api/automation/test?projectId=${projectId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // You'll need to add authentication headers here if required
      },
    });

    if (!debugResponse.ok) {
      console.error('❌ Debug request failed:', debugResponse.status, debugResponse.statusText);
      const errorText = await debugResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const debugData = await debugResponse.json();
    console.log('✅ Debug data received:');
    console.log('Project:', debugData.project);
    console.log('Automations found:', debugData.automations.length);
    console.log('Automations:', debugData.automations);

    // Now test triggering the automation
    console.log('\n🚀 Triggering automation...');
    const triggerResponse = await fetch('http://localhost:3000/api/automation/test', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId: projectId,
        fromStatus: 'lead',
        toStatus: 'prospect',
      }),
    });

    if (!triggerResponse.ok) {
      console.error('❌ Trigger request failed:', triggerResponse.status, triggerResponse.statusText);
      const errorText = await triggerResponse.text();
      console.error('Error details:', errorText);
      return;
    }

    const triggerData = await triggerResponse.json();
    console.log('✅ Automation trigger result:');
    console.log(triggerData);

  } catch (error) {
    console.error('❌ Test failed with error:', error);
  }
}

// Instructions for manual testing
console.log(`
🔧 AUTOMATION TESTING INSTRUCTIONS:

1. Update the projectId and automationId variables at the top of this file
2. Make sure your automation is configured with:
   - Trigger: "Project status moved"
   - Status: "prospect" (or whatever status you want to test)
   - Action: "Email"
   - Email Recipients: Your email address

3. Run this script: node test-automation.js

4. Check the console output for:
   - Debug information about your project and automations
   - Automation trigger results
   - Any error messages

5. Check your email for the automation notification

6. Check the server logs for automation processing messages

📝 TROUBLESHOOTING TIPS:

- If you get authentication errors, you may need to add proper auth headers
- If no automations are found, check that your automation is active
- If the trigger doesn't match, verify your automation trigger configuration
- Check the server console for detailed automation processing logs
`);

// Run the test
testAutomation();
