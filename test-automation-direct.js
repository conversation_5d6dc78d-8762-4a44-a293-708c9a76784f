// Direct automation testing without API authentication
// This tests the automation engine components directly

const mongoose = require('mongoose');

// Test configuration
const MONGODB_URI = 'mongodb://localhost:27017/trussi-ai'; // Update if different
const TEST_PROJECT_ID = '67e6f2c11c7eac73087cc900'; // Update with actual project ID
const TEST_ACCOUNT_ID = '507f1f77bcf86cd799439011'; // Update with actual account ID

async function testAutomationSystem() {
  try {
    console.log('🧪 Starting Direct Automation System Test...\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');

    // Test 1: Check if automations exist
    console.log('🔍 Test 1: Checking existing automations...');
    const Automation = mongoose.model('Automation');
    const automations = await Automation.find({}).limit(5);
    console.log(`Found ${automations.length} automations in database`);
    
    if (automations.length > 0) {
      console.log('Sample automation:');
      console.log(JSON.stringify(automations[0], null, 2));
    }
    console.log('');

    // Test 2: Check if projects exist
    console.log('🔍 Test 2: Checking existing projects...');
    const Project = mongoose.model('Project');
    const projects = await Project.find({}).limit(5);
    console.log(`Found ${projects.length} projects in database`);
    
    if (projects.length > 0) {
      console.log('Sample project:');
      const project = projects[0];
      console.log(`Project: ${project.name} (${project._id})`);
      console.log(`Status flags: isLead=${project.isLead}, isProspect=${project.isProspect}`);
      console.log(`Milestone: ${project.currentMilestone}`);
    }
    console.log('');

    // Test 3: Test status mapping function
    console.log('🔍 Test 3: Testing status mapping...');
    function getProjectStatus(project) {
      if (project.isCancelled) return 'canceled';
      if (project.isClosed) return 'completed';
      if (project.isLead) return 'lead';
      if (project.isProspect) return 'prospect';
      
      switch (project.currentMilestone) {
        case 'estimate': return 'estimate';
        case 'proposal': return 'proposal';
        case 'contract': return 'contract';
        case 'in_progress': return 'in_progress';
        case 'completed': return 'completed';
        default:
          if (project.isLead) return 'lead';
          if (project.isProspect) return 'prospect';
          return 'lead';
      }
    }

    if (projects.length > 0) {
      const testProject = projects[0];
      const status = getProjectStatus(testProject);
      console.log(`Project ${testProject.name} status: ${status}`);
    }
    console.log('');

    // Test 4: Create a test automation
    console.log('🔍 Test 4: Creating test automation...');
    const testAutomation = new Automation({
      name: 'Test Email Automation',
      description: 'Test automation for debugging',
      account: TEST_ACCOUNT_ID,
      isActive: true,
      trigger: {
        type: 'project_status_moved',
        projectStatusMovedConditions: {
          toStatus: 'prospect'
        }
      },
      actions: [{
        type: 'send_email',
        emailConfig: {
          subject: 'Test: Hello [customer_name]',
          body: 'Dear [customer_name], your project is now a prospect. Contact [project_lead] for details.',
          recipients: [{
            type: 'custom',
            email: '<EMAIL>'
          }]
        }
      }],
      createdBy: new mongoose.Types.ObjectId(),
      modifiedBy: new mongoose.Types.ObjectId(),
    });

    await testAutomation.save();
    console.log(`✅ Created test automation: ${testAutomation._id}`);
    console.log('');

    // Test 5: Test automation trigger logic
    console.log('🔍 Test 5: Testing automation trigger logic...');
    
    // Simulate the shouldTriggerAutomation logic
    function shouldTriggerAutomation(automation, previousStatus, newStatus) {
      const { trigger } = automation;
      
      console.log(`   Checking trigger: ${trigger.type}`);
      console.log(`   Previous: ${previousStatus}, New: ${newStatus}`);
      
      switch (trigger.type) {
        case 'project_status_changed':
          if (!trigger.projectStatusChangeConditions) return false;
          const { fromStatus, toStatus } = trigger.projectStatusChangeConditions;
          return previousStatus === fromStatus && newStatus === toStatus;

        case 'project_status_moved':
          if (!trigger.projectStatusMovedConditions) return false;
          const { toStatus: targetStatus } = trigger.projectStatusMovedConditions;
          return newStatus === targetStatus;

        default:
          return false;
      }
    }

    const shouldTrigger = shouldTriggerAutomation(testAutomation, 'lead', 'prospect');
    console.log(`   Should trigger: ${shouldTrigger}`);
    console.log('');

    // Test 6: Test template variable extraction
    console.log('🔍 Test 6: Testing template variable extraction...');
    
    function extractTemplateVariables(project) {
      const customer = project.customer || project.documents?.customer;
      const assignedTo = project.assignedTo || project.documents?.assignedTo;

      return {
        customer_name: customer?.name || `${customer?.firstName || ''} ${customer?.lastName || ''}`.trim() || 'Customer',
        customer_email: customer?.emailAddresses?.[0] || customer?.email || '',
        customer_phone: customer?.phoneNumbers?.[0] || customer?.phone || '',
        project_lead: assignedTo?.name || `${assignedTo?.firstName || ''} ${assignedTo?.lastName || ''}`.trim() || 'Project Lead',
      };
    }

    if (projects.length > 0) {
      const testProject = projects[0];
      const variables = extractTemplateVariables(testProject);
      console.log('Template variables:');
      console.log(JSON.stringify(variables, null, 2));
    }
    console.log('');

    // Cleanup
    console.log('🧹 Cleaning up test automation...');
    await Automation.findByIdAndDelete(testAutomation._id);
    console.log('✅ Test automation deleted');

    console.log('\n🎉 Direct automation system test completed!');
    console.log('\n📋 Summary:');
    console.log(`   - Automations in DB: ${automations.length}`);
    console.log(`   - Projects in DB: ${projects.length}`);
    console.log(`   - Status mapping: Working`);
    console.log(`   - Trigger logic: Working`);
    console.log(`   - Template variables: Working`);

  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
  }
}

// Run the test
testAutomationSystem();
