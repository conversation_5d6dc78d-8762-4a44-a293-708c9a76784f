import { Schema } from 'mongoose';

import { SCHEMAS } from '@/lib/schemas';
import { SchemaFactory } from '@/schemas/schema-factory';

import { AutomationMiddleware } from './middleware';
import { Automation, AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from './types';
import { migrationsV1 } from './migrations/v1';

export const AutomationSchema = new SchemaFactory<Automation>(
  SCHEMAS.AUTOMATION,
  {
    _id: { type: Schema.Types.ObjectId, auto: true, required: true },
    account: { type: Schema.Types.ObjectId, required: true },
    name: { type: String, required: true, trim: true },
    description: { type: String, trim: true },
    isActive: { type: Boolean, default: true },
    trigger: {
      type: {
        type: String,
        enum: Object.values(AUTOMATION_TRIGGER_TYPE),
        required: true,
      },
      conditions: { type: Schema.Types.Mixed },
      projectStatusChangeConditions: {
        fromStatus: { type: String, enum: Object.values(PROJECT_STATUS) },
        toStatus: { type: String, enum: Object.values(PROJECT_STATUS) },
        daysOfInactivity: { type: Number },
      },
      projectStatusMovedConditions: {
        toStatus: { type: String, enum: Object.values(PROJECT_STATUS) },
      },
    },
    actions: [
      {
        type: {
          type: String,
          enum: Object.values(AUTOMATION_ACTION_TYPE),
          required: true,
        },
        config: { type: Schema.Types.Mixed, default: {} },
        emailConfig: {
          subject: { type: String },
          body: { type: String },
          recipients: [{
            type: { type: String, enum: ['user', 'custom'] },
            userId: { type: String },
            email: { type: String },
          }],
        },
        smsConfig: {
          message: { type: String },
          recipients: [{
            type: { type: String, enum: ['user', 'custom'] },
            userId: { type: String },
            phoneNumber: { type: String },
          }],
        },
      },
    ],
    createdBy: { type: Schema.Types.ObjectId, required: true },
    modifiedBy: { type: Schema.Types.ObjectId, required: true },
  },
  {
    collection: 'automation',
    insertIntoSearchCollection: true,
  },
);

AutomationSchema.virtuals((schema) => {
  schema.virtual('documents.createdBy', {
    ref: 'User',
    localField: 'createdBy',
    foreignField: '_id',
    justOne: true,
  });

  schema.virtual('documents.modifiedBy', {
    ref: 'User',
    localField: 'modifiedBy',
    foreignField: '_id',
    justOne: true,
  });

  schema.virtual('documents.account', {
    ref: 'Account',
    localField: 'account',
    foreignField: '_id',
    justOne: true,
  });
});

AutomationSchema.migrate(async () => {
  for (const migration of migrationsV1) {
    await migration.up();
  }
});
AutomationSchema.middleware((schema) => {
  AutomationMiddleware(schema);
});
