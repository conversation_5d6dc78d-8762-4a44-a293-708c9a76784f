import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next';

import { Field as BaseField, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { AutomationToggle } from '@/components/ui/automation-toggle';
import { AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from '@/schemas/automations/types';

import { create } from './actions';
import { validatePermissions } from './permissions';
import { AutomationFormClient } from './automation-form-client';

// Type-safe wrapper for Field component with extended props
const Field = BaseField as React.ComponentType<{
  label?: string;
  name?: string;
  placeholder?: string;
  required?: boolean;
  autoFocus?: boolean;
  type?: string;
  options?: Array<{ value: string; label: string }>;
  min?: number;
  help?: string;
  className?: string;
  'data-autocomplete-type'?: string;
}>;

export async function generateMetadata(_: unknown, parent: ResolvingMetadata): Promise<Metadata> {
  const parentMetadata = await parent;

  return {
    title: 'Create Automation | ' + parentMetadata.title?.absolute,
  };
}

export default async function CreateAutomationPage() {
  await validatePermissions();

  return (
    <>
      <h1 className="text-2xl font-bold mb-6">Create Automation</h1>
      <AutomationFormClient />

      <Form action={create}>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-medium">Automation Details</h2>
          <AutomationToggle name="isActive" defaultChecked={true} />
        </div>
        <Widget>
          <div className="space-y-4 p-4">
            <Field label="Name" name="name" placeholder="Enter a name for this automation" required autoFocus />

            <Field
              label="Description"
              name="description"
              placeholder="Describe what this automation does"
              type="textarea"
            />

            <Field
              label="Trigger"
              name="triggerType"
              type="select"
              required
              options={Object.values(AUTOMATION_TRIGGER_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what triggers this automation"
            />

            <div className="trigger-conditions" data-trigger-type="project_status_changed" style={{ display: 'none' }}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Field
                  label="From Status"
                  name="fromStatus"
                  type="select"
                  options={Object.values(PROJECT_STATUS).map((value) => ({
                    value,
                    label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                  }))}
                  placeholder="Select original status"
                />

                <Field
                  label="To Status"
                  name="toStatus"
                  type="select"
                  options={Object.values(PROJECT_STATUS).map((value) => ({
                    value,
                    label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                  }))}
                  placeholder="Select new status"
                />
              </div>

              <Field
                label="Days of Inactivity"
                name="daysOfInactivity"
                type="number"
                min={0}
                help="Number of days with no activity before triggering"
                placeholder="Enter number of days"
              />
            </div>

            <div className="trigger-conditions" data-trigger-type="project_status_moved" style={{ display: 'none' }}>
              <Field
                label="Status"
                name="toStatus"
                type="select"
                options={Object.values(PROJECT_STATUS).map((value) => ({
                  value,
                  label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                }))}
                placeholder="Select status"
              />
            </div>

            <Field
              label="Action"
              name="actionType"
              type="select"
              required
              options={Object.values(AUTOMATION_ACTION_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what action to perform"
            />

            <div className="action-config" data-action-type="send_email" style={{ display: 'none' }}>
              <Field
                label="Email Subject"
                name="emailSubject"
                placeholder="Enter email subject"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />

              <Field
                label="Email Body"
                name="emailBody"
                type="textarea"
                placeholder="Enter email content"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />

              <Field
                label="Email Addresses"
                name="customEmails"
                placeholder="Enter email addresses, separated by commas"
                help="Available variables: [customer_email]. Example: [customer_email], <EMAIL>"
                className="user-autocomplete"
                data-autocomplete-type="email"
              />
            </div>

            <div className="action-config" data-action-type="send_sms" style={{ display: 'none' }}>
              <Field
                label="SMS Message"
                name="smsMessage"
                type="textarea"
                placeholder="Enter SMS message"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />

              <Field
                label="Phone Numbers"
                name="customPhones"
                placeholder="Enter phone numbers, separated by commas"
                help="Available variables: [customer_phone]. Example: [customer_phone], +1234567890"
                className="user-autocomplete"
                data-autocomplete-type="phone"
              />
            </div>

            <div className="action-config" data-action-type="email_and_sms" style={{ display: 'none' }}>
              <Field
                label="Email Subject"
                name="emailSubject"
                placeholder="Enter email subject"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />
              <Field
                label="Email Body"
                name="emailBody"
                type="textarea"
                placeholder="Enter email content"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />
              <Field
                label="SMS Message"
                name="smsMessage"
                type="textarea"
                placeholder="Enter SMS message"
                help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
              />

              <Field
                label="Email Addresses"
                name="customEmails"
                placeholder="Enter email addresses, separated by commas"
                help="Available variables: [customer_email]. Example: [customer_email], <EMAIL>"
                className="user-autocomplete"
                data-autocomplete-type="email"
              />

              <Field
                label="Phone Numbers"
                name="customPhones"
                placeholder="Enter phone numbers, separated by commas"
                help="Available variables: [customer_phone]. Example: [customer_phone], +1234567890"
                className="user-autocomplete"
                data-autocomplete-type="phone"
              />
            </div>

            <div className="action-config" data-action-type="in_app_notification" style={{ display: 'none' }}>
              <Field label="Notification Title" name="notificationTitle" placeholder="Enter notification title" />
              <Field label="Notification Message" name="notificationMessage" type="textarea" placeholder="Enter notification message" />

              <Field
                label="Recipients"
                name="notificationRecipientType"
                type="select"
                options={[
                  { value: 'user', label: 'Users in Organization' },
                  { value: 'custom', label: 'Specific Users' },
                ]}
                placeholder="Select recipient type"
              />

              <Field
                label="User IDs"
                name="customUserIds"
                placeholder="Enter user IDs, separated by commas"
                help="Only used when 'Specific Users' is selected"
                className="user-autocomplete"
                data-autocomplete-type="user"
              />
            </div>
          </div>
        </Widget>

        <ActionFooter>
          <Link href="/automation" className="text-muted-foreground">
            Cancel
          </Link>
          <FormButton>Create Automation</FormButton>
        </ActionFooter>
      </Form>
    </>
  );
}
