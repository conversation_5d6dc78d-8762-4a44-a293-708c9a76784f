import { getAutomationModel } from '@/schemas';
import { sendEmail, template } from '@/lib/mail';
import { 
  extractTemplateVariables, 
  processEmailTemplate, 
  processSmsTemplate 
} from '@/lib/automation-templates';
import type { 
  Automation, 
  AUTOMATION_TRIGGER_TYPE, 
  AUTOMATION_ACTION_TYPE,
  PROJECT_STATUS 
} from '@/schemas/automations/types';
import type { Project } from '@/schemas/projects/types';
import type { Customer } from '@/schemas/customers/types';
import type { User } from '@/schemas/users/types';

/**
 * Main automation execution engine
 */
export class AutomationEngine {
  /**
   * Process project status change and trigger relevant automations
   */
  static async processProjectStatusChange(
    project: Project & { 
      documents?: { 
        customer?: Customer; 
        assignedTo?: User; 
        account?: any;
      } 
    },
    previousStatus?: PROJECT_STATUS,
    newStatus?: PROJECT_STATUS
  ): Promise<void> {
    try {
      const automationModel = await getAutomationModel();
      
      // Find all active automations for this account
      const automations = await automationModel.find({
        account: project.account,
        isActive: true,
      }).populate([
        { path: 'createdBy', select: 'firstName lastName email' },
        { path: 'modifiedBy', select: 'firstName lastName email' },
      ]);

      for (const automation of automations) {
        if (await this.shouldTriggerAutomation(automation, project, previousStatus, newStatus)) {
          await this.executeAutomation(automation, project);
        }
      }
    } catch (error) {
      console.error('Error processing automation triggers:', error);
      // Don't throw to avoid disrupting the main project save operation
    }
  }

  /**
   * Check if an automation should be triggered
   */
  private static async shouldTriggerAutomation(
    automation: Automation,
    project: Project,
    previousStatus?: PROJECT_STATUS,
    newStatus?: PROJECT_STATUS
  ): Promise<boolean> {
    const { trigger } = automation;

    switch (trigger.type) {
      case 'project_status_changed':
        if (!trigger.projectStatusChangeConditions) return false;
        const { fromStatus, toStatus } = trigger.projectStatusChangeConditions;
        return previousStatus === fromStatus && newStatus === toStatus;

      case 'project_status_moved':
        if (!trigger.projectStatusMovedConditions) return false;
        const { toStatus: targetStatus } = trigger.projectStatusMovedConditions;
        return newStatus === targetStatus;

      default:
        return false;
    }
  }

  /**
   * Execute an automation's actions
   */
  private static async executeAutomation(
    automation: Automation,
    project: Project & { 
      documents?: { 
        customer?: Customer; 
        assignedTo?: User; 
      } 
    }
  ): Promise<void> {
    try {
      // Extract template variables from project data
      const templateVariables = extractTemplateVariables(project);

      for (const action of automation.actions) {
        await this.executeAction(action, templateVariables, project);
      }
    } catch (error) {
      console.error(`Error executing automation ${automation.name}:`, error);
    }
  }

  /**
   * Execute a specific action
   */
  private static async executeAction(
    action: any,
    templateVariables: any,
    project: Project
  ): Promise<void> {
    switch (action.type) {
      case 'send_email':
        await this.executeEmailAction(action, templateVariables, project);
        break;

      case 'send_sms':
        await this.executeSmsAction(action, templateVariables, project);
        break;

      case 'email_and_sms':
        await this.executeEmailAction(action, templateVariables, project);
        await this.executeSmsAction(action, templateVariables, project);
        break;

      case 'in_app_notification':
        await this.executeNotificationAction(action, templateVariables, project);
        break;

      default:
        console.warn(`Unknown action type: ${action.type}`);
    }
  }

  /**
   * Execute email action
   */
  private static async executeEmailAction(
    action: any,
    templateVariables: any,
    project: Project
  ): Promise<void> {
    if (!action.emailConfig) return;

    try {
      const processedEmail = processEmailTemplate(action.emailConfig, templateVariables);
      
      if (processedEmail.recipients.length === 0) {
        console.warn('No email recipients found for automation');
        return;
      }

      const emailAddresses = processedEmail.recipients
        .map(r => r.email)
        .filter(email => email && email.trim() !== '');

      if (emailAddresses.length === 0) {
        console.warn('No valid email addresses found after template processing');
        return;
      }

      await sendEmail({
        from: '"Trussi.AI Automation" <<EMAIL>>',
        to: emailAddresses,
        subject: processedEmail.subject || 'Automation Notification',
        html: template(processedEmail.body || 'Automation triggered'),
      });

      console.log(`Email sent for automation to: ${emailAddresses.join(', ')}`);
    } catch (error) {
      console.error('Error sending automation email:', error);
    }
  }

  /**
   * Execute SMS action
   */
  private static async executeSmsAction(
    action: any,
    templateVariables: any,
    project: Project
  ): Promise<void> {
    if (!action.smsConfig) return;

    try {
      const processedSms = processSmsTemplate(action.smsConfig, templateVariables);
      
      // SMS implementation would go here
      // For now, just log the SMS that would be sent
      console.log('SMS would be sent:', {
        message: processedSms.message,
        recipients: processedSms.recipients.map(r => r.phoneNumber),
      });
    } catch (error) {
      console.error('Error sending automation SMS:', error);
    }
  }

  /**
   * Execute in-app notification action
   */
  private static async executeNotificationAction(
    action: any,
    templateVariables: any,
    project: Project
  ): Promise<void> {
    try {
      // In-app notification implementation would go here
      console.log('In-app notification would be sent:', {
        title: action.config?.title,
        message: action.config?.message,
        recipientType: action.config?.recipientType,
        userIds: action.config?.userIds,
      });
    } catch (error) {
      console.error('Error sending automation notification:', error);
    }
  }
}
