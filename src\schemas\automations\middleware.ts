import mongoose from 'mongoose';

import { getAccountModel } from '@/schemas/accounts';
import { getUserModel } from '@/schemas/users';
import { logger } from '@/lib/logger';

import { Automation, AutomationDoc } from './types';

export function AutomationMiddleware(schema: mongoose.Schema<Automation>) {
  // Virtual fields are already defined in the schema, no need to duplicate them here

  schema.method('populate', async function (this: AutomationDoc) {
    try {
      if (!this.populated('documents.account') && this.account) {
        const accountModel = await getAccountModel();
        const account = await accountModel.findOne({ _id: this.account });
        this.documents = {
          ...this.documents,
          account: account || undefined,
        };
      }

      if (!this.populated('documents.createdBy') && this.createdBy) {
        const userModel = await getUserModel();
        const createdBy = await userModel.findOne({ _id: this.createdBy });
        this.documents = {
          ...this.documents,
          createdBy: createdBy || undefined,
        };
      }

      if (!this.populated('documents.modifiedBy') && this.modifiedBy) {
        const userModel = await getUserModel();
        const modifiedBy = await userModel.findOne({ _id: this.modifiedBy });
        this.documents = {
          ...this.documents,
          modifiedBy: modifiedBy || undefined,
        };
      }
    } catch (error) {
      logger.error('Error in AutomationMiddleware.populate:', error);
    }
  });
}
