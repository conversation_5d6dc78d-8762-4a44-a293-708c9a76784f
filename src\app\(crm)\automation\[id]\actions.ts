'use server';

import { redirect } from 'next/navigation';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';
import { ActionResult } from '@/lib/form/types';
import { AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from '@/schemas/automations/types';
import { validateTemplate } from '@/lib/automation-templates';

import { getData } from './helpers';

export async function edit(id: string, _: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    const { automation, user, canEdit } = await getData(id);

    if (!canEdit) {
      return { error: 'You do not have permission to edit this automation' };
    }

    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const triggerType = formData.get('triggerType') as string;
    const actionType = formData.get('actionType') as string;
    const isActive = formData.get('isActive') === 'on';

    if (!name) {
      return { error: 'Name is required' };
    }

    if (!triggerType) {
      return { error: 'Trigger type is required' };
    }

    if (!actionType) {
      return { error: 'Action type is required' };
    }

    // Update basic automation properties
    automation.name = name;
    automation.description = description;
    automation.isActive = isActive;
    automation.trigger.type = triggerType as AUTOMATION_TRIGGER_TYPE;
    automation.actions[0].type = actionType as AUTOMATION_ACTION_TYPE;
    automation.modifiedBy = user._id;

    // Handle project status change trigger conditions
    if (triggerType === 'project_status_changed') {
      const fromStatus = formData.get('fromStatus') as string;
      const toStatus = formData.get('toStatus') as string;
      const daysOfInactivity = parseInt((formData.get('daysOfInactivity') as string) || '0', 10);

      automation.trigger.projectStatusChangeConditions = {
        fromStatus: fromStatus as PROJECT_STATUS,
        toStatus: toStatus as PROJECT_STATUS,
        daysOfInactivity,
      };
      // Clear project status moved conditions
      automation.trigger.projectStatusMovedConditions = undefined;
    } else if (triggerType === 'project_status_moved') {
      const toStatus = formData.get('toStatus') as string;

      automation.trigger.projectStatusMovedConditions = {
        toStatus: toStatus as PROJECT_STATUS,
      };
      // Clear project status change conditions
      automation.trigger.projectStatusChangeConditions = undefined;
    } else {
      // Clear both trigger conditions if trigger type is different
      automation.trigger.projectStatusChangeConditions = undefined;
      automation.trigger.projectStatusMovedConditions = undefined;
    }

    // Handle email action configuration
    if (actionType === 'send_email') {
      const emailSubject = formData.get('emailSubject') as string;
      const emailBody = formData.get('emailBody') as string;
      const customEmails = formData.get('customEmails') as string;

      // Validate template variables in email fields
      const subjectValidation = validateTemplate(emailSubject || '');
      const bodyValidation = validateTemplate(emailBody || '');
      const emailsValidation = validateTemplate(customEmails || '');

      if (!subjectValidation.isValid) {
        return { error: `Invalid template variables in email subject: ${subjectValidation.invalidVariables.join(', ')}` };
      }
      if (!bodyValidation.isValid) {
        return { error: `Invalid template variables in email body: ${bodyValidation.invalidVariables.join(', ')}` };
      }
      if (!emailsValidation.isValid) {
        return { error: `Invalid template variables in email addresses: ${emailsValidation.invalidVariables.join(', ')}` };
      }

      const recipients = [];
      if (customEmails) {
        const emails = customEmails.split(',').map((email) => email.trim());
        for (const email of emails) {
          if (email) {
            recipients.push({
              type: 'custom' as const,
              email,
            });
          }
        }
      }

      automation.actions[0].emailConfig = {
        subject: emailSubject,
        body: emailBody,
        recipients,
      };

      // Clear SMS config and general config if action type is email
      automation.actions[0].smsConfig = undefined;
      automation.actions[0].config = {};
    }

    // Handle SMS action configuration
    if (actionType === 'send_sms') {
      const smsMessage = formData.get('smsMessage') as string;
      const customPhones = formData.get('customPhones') as string;

      const recipients = [];
      if (customPhones) {
        const phones = customPhones.split(',').map((phone) => phone.trim());
        for (const phoneNumber of phones) {
          if (phoneNumber) {
            recipients.push({
              type: 'custom' as const,
              phoneNumber,
            });
          }
        }
      }

      automation.actions[0].smsConfig = {
        message: smsMessage,
        recipients,
      };

      // Clear email config if action type is SMS
      automation.actions[0].emailConfig = undefined;
      automation.actions[0].config = {};
    }

    // Handle Email & SMS action configuration
    if (actionType === 'email_and_sms') {
      const emailSubject = formData.get('emailSubject') as string;
      const emailBody = formData.get('emailBody') as string;
      const smsMessage = formData.get('smsMessage') as string;
      const customEmails = formData.get('customEmails') as string;
      const customPhones = formData.get('customPhones') as string;

      const emailRecipients = [];
      const smsRecipients = [];

      if (customEmails) {
        const emails = customEmails.split(',').map((email) => email.trim());
        for (const email of emails) {
          if (email) {
            emailRecipients.push({
              type: 'custom' as const,
              email,
            });
          }
        }
      }

      if (customPhones) {
        const phones = customPhones.split(',').map((phone) => phone.trim());
        for (const phoneNumber of phones) {
          if (phoneNumber) {
            smsRecipients.push({
              type: 'custom' as const,
              phoneNumber,
            });
          }
        }
      }

      automation.actions[0].emailConfig = {
        subject: emailSubject,
        body: emailBody,
        recipients: emailRecipients,
      };

      automation.actions[0].smsConfig = {
        message: smsMessage,
        recipients: smsRecipients,
      };

      automation.actions[0].config = {};
    }

    // Handle In App Notification action configuration
    if (actionType === 'in_app_notification') {
      const notificationTitle = formData.get('notificationTitle') as string;
      const notificationMessage = formData.get('notificationMessage') as string;
      const notificationRecipientType = formData.get('notificationRecipientType') as string;
      const customUserIds = formData.get('customUserIds') as string;

      const config: Record<string, unknown> = {
        title: notificationTitle,
        message: notificationMessage,
        recipientType: notificationRecipientType,
      };

      if (notificationRecipientType === 'custom' && customUserIds) {
        const userIds = customUserIds.split(',').map((id) => id.trim()).filter(Boolean);
        config.userIds = userIds;
      }

      automation.actions[0].config = config;

      // Clear email and SMS configs
      automation.actions[0].emailConfig = undefined;
      automation.actions[0].smsConfig = undefined;
    }

    await automation.save();

    return { error: null, message: 'Automation updated successfully' };
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }
}

export async function remove(id: string): Promise<ActionResult> {
  'use server';
  try {
    const { canDelete } = await getData(id);

    if (!canDelete) {
      return { error: 'You do not have permission to delete this automation' };
    }

    const automationModel = await getAutomationModel();
    await automationModel.deleteOne({ _id: new mongoose.Types.ObjectId(id) });
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }

  // Redirect outside of try-catch to avoid NEXT_REDIRECT error
  redirect('/automation');
}
