import type { Customer } from '@/schemas/customers';
import type { Project } from '@/schemas/projects';
import type { User } from '@/schemas/users';

/**
 * Template variables that can be used in automation messages
 */
export interface TemplateVariables {
  customer_name: string;
  customer_email: string;
  customer_phone: string;
  project_lead: string;
}

/**
 * Extract template variables from project data
 */
export function extractTemplateVariables(
  project: Project & { documents?: { customer?: Customer; assignedTo?: User } }
): TemplateVariables {
  const customer = project.documents?.customer;
  const assignedTo = project.documents?.assignedTo;

  return {
    customer_name: customer?.name || `${customer?.firstName || ''} ${customer?.lastName || ''}`.trim() || 'Customer',
    customer_email: customer?.emailAddresses?.[0] || customer?.email || '',
    customer_phone: customer?.phoneNumbers?.[0] || customer?.phone || '',
    project_lead: assignedTo?.name || `${assignedTo?.firstName || ''} ${assignedTo?.lastName || ''}`.trim() || 'Project Lead',
  };
}

/**
 * Replace template variables in a string with actual values
 */
export function replaceTemplateVariables(template: string, variables: TemplateVariables): string {
  let result = template;

  // Replace each variable with its value
  Object.entries(variables).forEach(([key, value]) => {
    const placeholder = `[${key}]`;
    result = result.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
  });

  return result;
}

/**
 * Process template variables in email configuration
 */
export function processEmailTemplate(
  emailConfig: { subject: string; body: string; recipients: Array<{ type: string; email: string }> },
  variables: TemplateVariables
): { subject: string; body: string; recipients: Array<{ type: string; email: string }> } {
  return {
    subject: replaceTemplateVariables(emailConfig.subject, variables),
    body: replaceTemplateVariables(emailConfig.body, variables),
    recipients: emailConfig.recipients.map(recipient => ({
      ...recipient,
      email: replaceTemplateVariables(recipient.email, variables),
    })),
  };
}

/**
 * Process template variables in SMS configuration
 */
export function processSmsTemplate(
  smsConfig: { message: string; recipients: Array<{ type: string; phoneNumber: string }> },
  variables: TemplateVariables
): { message: string; recipients: Array<{ type: string; phoneNumber: string }> } {
  return {
    message: replaceTemplateVariables(smsConfig.message, variables),
    recipients: smsConfig.recipients.map(recipient => ({
      ...recipient,
      phoneNumber: replaceTemplateVariables(recipient.phoneNumber, variables),
    })),
  };
}

/**
 * Get list of available template variables for help text
 */
export function getAvailableVariables(): string[] {
  return ['[customer_name]', '[customer_email]', '[customer_phone]', '[project_lead]'];
}

/**
 * Validate that a template string contains only valid variables
 */
export function validateTemplate(template: string): { isValid: boolean; invalidVariables: string[] } {
  const variablePattern = /\[([^\]]+)\]/g;
  const validVariables = ['customer_name', 'customer_email', 'customer_phone', 'project_lead'];
  const invalidVariables: string[] = [];
  
  let match;
  while ((match = variablePattern.exec(template)) !== null) {
    const variableName = match[1];
    if (!validVariables.includes(variableName)) {
      invalidVariables.push(`[${variableName}]`);
    }
  }

  return {
    isValid: invalidVariables.length === 0,
    invalidVariables,
  };
}
