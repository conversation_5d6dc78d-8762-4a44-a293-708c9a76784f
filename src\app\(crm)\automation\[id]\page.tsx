import Link from 'next/link';
import { Metadata, ResolvingMetadata } from 'next';
import { Trash2 } from 'lucide-react';

import { Action, Field as BaseField, Form, FormButton } from '@/lib/form';
import { ActionFooter } from '@/lib/nav';
import { Widget } from '@/lib/widget';
import { AutomationToggle } from '@/components/ui/automation-toggle';
import { AUTOMATION_ACTION_TYPE, AUTOMATION_TRIGGER_TYPE, PROJECT_STATUS } from '@/schemas/automations/types';
import { Button } from '@/components/ui/button';
import { AutomationFormClient } from '@/app/(crm)/automation/create/automation-form-client';

import { edit, remove } from './actions';
import { getData } from './helpers';

// Type-safe wrapper for Field component with extended props
const Field = BaseField as React.ComponentType<{
  label?: string;
  name?: string;
  placeholder?: string;
  required?: boolean;
  value?: string;
  defaultValue?: string;
  readOnly?: boolean;
  type?: string;
  options?: Array<{ value: string; label: string }>;
  min?: number;
  help?: string;
  className?: string;
  'data-autocomplete-type'?: string;
}>;

export async function generateMetadata(
  { params }: { params: { id: string } },
  parent: ResolvingMetadata,
): Promise<Metadata> {
  const { automation } = await getData(params.id);
  const parentMetadata = await parent;

  return {
    title: `${automation.name} | ${parentMetadata.title?.absolute}`,
  };
}

export default async function AutomationDetailPage({ params }: { params: { id: string } }) {
  const { automation, canEdit, canDelete } = await getData(params.id);

  const editAction = edit.bind(null, params.id);
  const removeAction = remove.bind(null, params.id);

  return (
    <>
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">{automation.name}</h1>
        {canDelete && (
          <Form action={removeAction}>
            <Action>
              <Button type="submit" variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </Action>
          </Form>
        )}
      </div>

      <AutomationFormClient />

      <Form action={editAction}>
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-lg font-medium">Automation Details</h2>
          <AutomationToggle name="isActive" defaultChecked={automation.isActive} disabled={!canEdit} />
        </div>
        <Widget>
          <div className="space-y-4 p-4">
            <Field
              label="Name"
              name="name"
              placeholder="Enter a name for this automation"
              required
              defaultValue={automation.name}
              readOnly={!canEdit}
            />

            <Field
              label="Description"
              name="description"
              placeholder="Describe what this automation does"
              type="textarea"
              defaultValue={automation.description}
              readOnly={!canEdit}
            />

            <Field
              label="Trigger"
              name="triggerType"
              type="select"
              required
              defaultValue={automation.trigger?.type}
              options={Object.values(AUTOMATION_TRIGGER_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what triggers this automation"
              readOnly={!canEdit}
            />

            {automation.trigger.type === 'project_status_changed' && (
              <div className="trigger-conditions">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Field
                    label="From Status"
                    name="fromStatus"
                    type="select"
                    options={Object.values(PROJECT_STATUS).map((value) => ({
                      value,
                      label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                    }))}
                    placeholder="Select original status"
                    defaultValue={automation.trigger.projectStatusChangeConditions?.fromStatus}
                    readOnly={!canEdit}
                  />

                  <Field
                    label="To Status"
                    name="toStatus"
                    type="select"
                    options={Object.values(PROJECT_STATUS).map((value) => ({
                      value,
                      label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                    }))}
                    placeholder="Select new status"
                    defaultValue={automation.trigger.projectStatusChangeConditions?.toStatus}
                    readOnly={!canEdit}
                  />
                </div>

                <Field
                  label="Days of Inactivity"
                  name="daysOfInactivity"
                  type="number"
                  min={0}
                  placeholder="Enter number of days"
                  defaultValue={automation.trigger.projectStatusChangeConditions?.daysOfInactivity?.toString()}
                  readOnly={!canEdit}
                />
              </div>
            )}

            {automation.trigger.type === 'project_status_moved' && (
              <div className="trigger-conditions">
                <Field
                  label="Status"
                  name="toStatus"
                  type="select"
                  options={Object.values(PROJECT_STATUS).map((value) => ({
                    value,
                    label: value.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
                  }))}
                  placeholder="Select status"
                  defaultValue={automation.trigger.projectStatusMovedConditions?.toStatus}
                  readOnly={!canEdit}
                />
              </div>
            )}

            <Field
              label="Action"
              name="actionType"
              type="select"
              required
              options={Object.values(AUTOMATION_ACTION_TYPE).map((value) => ({
                value,
                label: value.replace(/_/g, ' '),
              }))}
              placeholder="Select what action to perform"
              defaultValue={automation.actions[0]?.type}
              readOnly={!canEdit}
            />

            {automation.actions[0]?.type === 'send_email' && (
              <div className="action-config" data-action-type="send_email">
                <Field
                  label="Email Subject"
                  name="emailSubject"
                  placeholder="Enter email subject"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.emailConfig?.subject}
                  readOnly={!canEdit}
                />

                <Field
                  label="Email Body"
                  name="emailBody"
                  type="textarea"
                  placeholder="Enter email content"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.emailConfig?.body}
                  readOnly={!canEdit}
                />

                <Field
                  label="Email Addresses"
                  name="customEmails"
                  placeholder="Enter email addresses, separated by commas"
                  help="Available variables: [customer_email]. Example: [customer_email], <EMAIL>"
                  defaultValue={automation.actions[0]?.emailConfig?.recipients
                    ?.filter((r) => r.type === 'custom')
                    ?.map((r) => r.email)
                    ?.join(', ')}
                  readOnly={!canEdit}
                  className="user-autocomplete"
                  data-autocomplete-type="email"
                />
              </div>
            )}

            {automation.actions[0]?.type === 'send_sms' && (
              <div className="action-config" data-action-type="send_sms">
                <Field
                  label="SMS Message"
                  name="smsMessage"
                  type="textarea"
                  placeholder="Enter SMS message"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.smsConfig?.message}
                  readOnly={!canEdit}
                />

                <Field
                  label="Phone Numbers"
                  name="customPhones"
                  placeholder="Enter phone numbers, separated by commas"
                  help="Available variables: [customer_phone]. Example: [customer_phone], +1234567890"
                  defaultValue={automation.actions[0]?.smsConfig?.recipients
                    ?.filter((r) => r.type === 'custom')
                    ?.map((r) => r.phoneNumber)
                    ?.join(', ')}
                  readOnly={!canEdit}
                  className="user-autocomplete"
                  data-autocomplete-type="phone"
                />
              </div>
            )}

            {automation.actions[0]?.type === 'email_and_sms' && (
              <div className="action-config" data-action-type="email_and_sms">
                <Field
                  label="Email Subject"
                  name="emailSubject"
                  placeholder="Enter email subject"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.emailConfig?.subject}
                  readOnly={!canEdit}
                />

                <Field
                  label="Email Body"
                  name="emailBody"
                  type="textarea"
                  placeholder="Enter email content"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.emailConfig?.body}
                  readOnly={!canEdit}
                />

                <Field
                  label="SMS Message"
                  name="smsMessage"
                  type="textarea"
                  placeholder="Enter SMS message"
                  help="Available variables: [customer_name], [customer_email], [customer_phone], [project_lead]"
                  defaultValue={automation.actions[0]?.smsConfig?.message}
                  readOnly={!canEdit}
                />

                <Field
                  label="Email Addresses"
                  name="customEmails"
                  placeholder="Enter email addresses, separated by commas"
                  help="Available variables: [customer_email]. Example: [customer_email], <EMAIL>"
                  defaultValue={automation.actions[0]?.emailConfig?.recipients
                    ?.filter((r) => r.type === 'custom')
                    ?.map((r) => r.email)
                    ?.join(', ')}
                  readOnly={!canEdit}
                  className="user-autocomplete"
                  data-autocomplete-type="email"
                />

                <Field
                  label="Phone Numbers"
                  name="customPhones"
                  placeholder="Enter phone numbers, separated by commas"
                  help="Available variables: [customer_phone]. Example: [customer_phone], +1234567890"
                  defaultValue={automation.actions[0]?.smsConfig?.recipients
                    ?.filter((r) => r.type === 'custom')
                    ?.map((r) => r.phoneNumber)
                    ?.join(', ')}
                  readOnly={!canEdit}
                  className="user-autocomplete"
                  data-autocomplete-type="phone"
                />
              </div>
            )}

            {automation.actions[0]?.type === 'in_app_notification' && (
              <div className="action-config" data-action-type="in_app_notification">
                <Field
                  label="Notification Title"
                  name="notificationTitle"
                  placeholder="Enter notification title"
                  defaultValue={automation.actions[0]?.config?.title as string}
                  readOnly={!canEdit}
                />

                <Field
                  label="Notification Message"
                  name="notificationMessage"
                  type="textarea"
                  placeholder="Enter notification message"
                  defaultValue={automation.actions[0]?.config?.message as string}
                  readOnly={!canEdit}
                />

                <Field
                  label="Recipients"
                  name="notificationRecipientType"
                  type="select"
                  options={[
                    { value: 'user', label: 'Users in Organization' },
                    { value: 'custom', label: 'Specific Users' },
                  ]}
                  placeholder="Select recipient type"
                  defaultValue={automation.actions[0]?.config?.recipientType as string}
                  readOnly={!canEdit}
                />

                {automation.actions[0]?.config?.recipientType === 'custom' && (
                  <Field
                    label="User IDs"
                    name="customUserIds"
                    placeholder="Enter user IDs, separated by commas"
                    help="Only used when 'Specific Users' is selected"
                    defaultValue={(automation.actions[0]?.config?.userIds as string[])?.join(', ')}
                    readOnly={!canEdit}
                    className="user-autocomplete"
                    data-autocomplete-type="user"
                  />
                )}
              </div>
            )}
          </div>
        </Widget>

        <Widget label="Automation History">
          <div className="p-4 space-y-2">
            <div className="text-sm">
              <span className="text-muted-foreground">Created by:</span>{' '}
              {automation.documents?.createdBy?.firstName && automation.documents?.createdBy?.lastName
                ? `${automation.documents.createdBy.firstName} ${automation.documents.createdBy.lastName}`
                : automation.documents?.createdBy?.email || 'Unknown'}{' '}
              on {new Date(automation.created).toLocaleDateString()}
            </div>
            <div className="text-sm">
              <span className="text-muted-foreground">Last modified by:</span>{' '}
              {automation.documents?.modifiedBy?.firstName && automation.documents?.modifiedBy?.lastName
                ? `${automation.documents.modifiedBy.firstName} ${automation.documents.modifiedBy.lastName}`
                : automation.documents?.modifiedBy?.email || 'Unknown'}{' '}
              on {new Date(automation.modified).toLocaleDateString()}
            </div>
          </div>
        </Widget>

        <ActionFooter>
          <Link href="/automation" className="text-muted-foreground">
            Back to Automations
          </Link>
          {canEdit && <FormButton>Save Changes</FormButton>}
        </ActionFooter>
      </Form>
    </>
  );
}
