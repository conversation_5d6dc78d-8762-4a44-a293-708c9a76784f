import { NextRequest, NextResponse } from 'next/server';
import mongoose from 'mongoose';

import { getAutomationModel, getProjectModel } from '@/schemas';
import { AutomationEngine } from '@/lib/automation-engine';
import { protectRoute, ProtectError<PERSON>andler } from '@/server';
import { extractTemplateVariables } from '@/lib/automation-templates';
import type { PROJECT_STATUS } from '@/schemas/automations/types';

/**
 * Test endpoint to manually trigger automations for debugging
 * POST /api/automation/test
 * Body: { projectId: string, fromStatus?: string, toStatus: string }
 */
export async function POST(request: NextRequest) {
  try {
    const { user } = await protectRoute();

    const body = await request.json();
    const { projectId, fromStatus, toStatus } = body;

    if (!projectId || !toStatus) {
      return NextResponse.json(
        { error: 'projectId and toStatus are required' },
        { status: 400 }
      );
    }

    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      return NextResponse.json(
        { error: 'Invalid project ID' },
        { status: 400 }
      );
    }

    // Get the project
    const projectModel = await getProjectModel();
    const project = await projectModel.findOne({
      _id: new mongoose.Types.ObjectId(projectId),
      account: user.account,
    }).populate([
      { path: 'customer', select: 'firstName lastName name email emailAddresses phone phoneNumbers' },
      { path: 'assignedTo', select: 'firstName lastName name email' },
      { path: 'account', select: 'name' },
    ]);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Get automations for this account
    const automationModel = await getAutomationModel();
    const automations = await automationModel.find({
      account: user.account,
      isActive: true,
    });

    console.log(`Found ${automations.length} active automations for account ${user.account}`);

    // Extract template variables for debugging
    const templateVariables = extractTemplateVariables(project);
    console.log('Template variables extracted:', templateVariables);

    // Manually trigger automation processing
    console.log(`Triggering automation: ${fromStatus} -> ${toStatus}`);
    await AutomationEngine.processProjectStatusChange(
      project,
      fromStatus as PROJECT_STATUS,
      toStatus as PROJECT_STATUS
    );

    return NextResponse.json({
      success: true,
      message: 'Automation test completed',
      data: {
        projectId,
        projectName: project.name,
        fromStatus,
        toStatus,
        automationsFound: automations.length,
        customerData: {
          name: project.documents?.customer?.name,
          email: project.documents?.customer?.emailAddresses?.[0],
          phone: project.documents?.customer?.phoneNumbers?.[0],
        },
        assignedTo: {
          name: project.documents?.assignedTo?.name,
          email: project.documents?.assignedTo?.email,
        },
        templateVariables,
        automationDetails: automations.map(automation => ({
          id: automation._id,
          name: automation.name,
          isActive: automation.isActive,
          trigger: automation.trigger,
          actions: automation.actions,
        })),
      },
    });
  } catch (error) {
    console.error('Error in automation test:', error);
    return ProtectErrorHandler(error);
  }
}

/**
 * Get automation debug info
 * GET /api/automation/test?projectId=xxx
 */
export async function GET(request: NextRequest) {
  try {
    const { user } = await protectRoute();

    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');

    if (!projectId || !mongoose.Types.ObjectId.isValid(projectId)) {
      return NextResponse.json(
        { error: 'Valid projectId is required' },
        { status: 400 }
      );
    }

    // Get the project
    const projectModel = await getProjectModel();
    const project = await projectModel.findOne({
      _id: new mongoose.Types.ObjectId(projectId),
      account: user.account,
    }).populate([
      { path: 'customer', select: 'firstName lastName name email emailAddresses phone phoneNumbers' },
      { path: 'assignedTo', select: 'firstName lastName name email' },
      { path: 'account', select: 'name' },
    ]);

    if (!project) {
      return NextResponse.json(
        { error: 'Project not found' },
        { status: 404 }
      );
    }

    // Get automations for this account
    const automationModel = await getAutomationModel();
    const automations = await automationModel.find({
      account: user.account,
    });

    return NextResponse.json({
      project: {
        id: project._id,
        name: project.name,
        isLead: project.isLead,
        isProspect: project.isProspect,
        currentMilestone: project.currentMilestone,
        customer: project.documents?.customer,
        assignedTo: project.documents?.assignedTo,
      },
      automations: automations.map(automation => ({
        id: automation._id,
        name: automation.name,
        isActive: automation.isActive,
        trigger: automation.trigger,
        actions: automation.actions,
      })),
    });
  } catch (error) {
    console.error('Error getting automation debug info:', error);
    return ProtectErrorHandler(error);
  }
}
