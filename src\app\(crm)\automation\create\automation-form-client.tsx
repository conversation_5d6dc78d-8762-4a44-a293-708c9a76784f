'use client';

import { useEffect } from 'react';

import { AutomationErrorBoundary } from '@/components/ui/automation-error-boundary';

function AutomationFormClientInner() {
  useEffect(() => {
    // Function to show/hide trigger conditions based on selected trigger
    function updateTriggerConditions() {
      const triggerSelect = document.querySelector('select[name="triggerType"]') as HTMLSelectElement;
      if (!triggerSelect) return;

      const selectedTrigger = triggerSelect.value;
      const projectStatusChangedFields = document.querySelector(
        '.trigger-conditions[data-trigger-type="project_status_changed"]',
      ) as HTMLElement;
      const projectStatusMovedFields = document.querySelector(
        '.trigger-conditions[data-trigger-type="project_status_moved"]',
      ) as HTMLElement;

      if (projectStatusChangedFields) {
        projectStatusChangedFields.style.display = selectedTrigger === 'project_status_changed' ? 'block' : 'none';
      }
      if (projectStatusMovedFields) {
        projectStatusMovedFields.style.display = selectedTrigger === 'project_status_moved' ? 'block' : 'none';
      }
    }

    // Function to show/hide action configurations based on selected action
    function updateActionConfigs() {
      const actionSelect = document.querySelector('select[name="actionType"]') as HTMLSelectElement;
      if (!actionSelect) return;

      const selectedAction = actionSelect.value;

      // Hide all action configuration sections
      document.querySelectorAll('.action-config').forEach((section) => {
        (section as HTMLElement).style.display = 'none';
      });

      // Show the appropriate action configuration section
      if (selectedAction === 'send_email') {
        const emailConfig = document.querySelector('.action-config[data-action-type="send_email"]') as HTMLElement;
        if (emailConfig) {
          emailConfig.style.display = 'block';
        }
      } else if (selectedAction === 'send_sms') {
        const smsConfig = document.querySelector('.action-config[data-action-type="send_sms"]') as HTMLElement;
        if (smsConfig) {
          smsConfig.style.display = 'block';
        }
      } else if (selectedAction === 'email_and_sms') {
        const emailAndSmsConfig = document.querySelector('.action-config[data-action-type="email_and_sms"]') as HTMLElement;
        if (emailAndSmsConfig) {
          emailAndSmsConfig.style.display = 'block';
        }
      } else if (selectedAction === 'in_app_notification') {
        const notificationConfig = document.querySelector('.action-config[data-action-type="in_app_notification"]') as HTMLElement;
        if (notificationConfig) {
          notificationConfig.style.display = 'block';
        }
      }
    }

    // Debounce function for autocomplete
    function debounce<T extends (...args: unknown[]) => unknown>(func: T, wait: number): T {
      let timeout: NodeJS.Timeout;
      return ((...args: Parameters<T>) => {
        clearTimeout(timeout);
        timeout = setTimeout(() => func(...args), wait);
      }) as T;
    }

    // Function to fetch user suggestions
    async function fetchUserSuggestions(query: string, type: string) {
      try {
        const response = await fetch(`/api/users/suggestions?query=${encodeURIComponent(query)}&type=${type}`);
        if (!response.ok) throw new Error('Failed to fetch suggestions');
        return await response.json();
      } catch (error) {
        // Error handling - in production you might want to log this to a service
        return [];
      }
    }

    // Function to create and show suggestions dropdown
    function showSuggestions(
      input: HTMLInputElement,
      suggestions: { name: string; email?: string; phone?: string }[],
      type: string,
    ) {
      // Remove any existing dropdown
      const existingDropdown = document.getElementById('autocomplete-dropdown');
      if (existingDropdown) existingDropdown.remove();

      // Create dropdown container
      const dropdown = document.createElement('div');
      dropdown.id = 'autocomplete-dropdown';
      dropdown.className = 'absolute z-50 bg-white border rounded-md shadow-lg mt-1 w-full max-h-60 overflow-y-auto';

      // Add suggestions to dropdown
      if (suggestions.length === 0) {
        const noResults = document.createElement('div');
        noResults.className = 'px-4 py-2 text-sm text-gray-500';
        noResults.textContent = 'No results found';
        dropdown.appendChild(noResults);
      } else {
        suggestions.forEach((suggestion) => {
          const item = document.createElement('div');
          item.className = 'px-4 py-2 hover:bg-gray-100 cursor-pointer text-sm';

          if (type === 'email') {
            item.textContent = `${suggestion.name} <${suggestion.email}>`;
          } else {
            item.textContent = `${suggestion.name} (${suggestion.phone})`;
          }

          item.addEventListener('click', () => {
            // Get current value and add the new suggestion
            const currentValue = input.value;
            const values = currentValue
              .split(',')
              .map((v) => v.trim())
              .filter((v) => v);

            // Add the new value
            const newValue = type === 'email' ? suggestion.email : suggestion.phone;
            if (newValue && !values.includes(newValue)) {
              values.push(newValue);
            }

            // Update input value
            input.value = values.join(', ');

            // Remove dropdown
            dropdown.remove();
          });

          dropdown.appendChild(item);
        });
      }

      // Position and append dropdown
      const rect = input.getBoundingClientRect();
      dropdown.style.width = `${rect.width}px`;

      // Append to body and position
      document.body.appendChild(dropdown);
      dropdown.style.position = 'absolute';
      dropdown.style.left = `${rect.left}px`;
      dropdown.style.top = `${rect.bottom + window.scrollY}px`;

      // Add event listener to close dropdown when clicking outside
      const closeDropdown = (e: Event) => {
        if (!dropdown.contains(e.target as Node) && e.target !== input) {
          dropdown.remove();
          document.removeEventListener('click', closeDropdown);
        }
      };
      document.addEventListener('click', closeDropdown);
    }

    // Initialize form interactions
    function initializeForm() {
      const triggerSelect = document.querySelector('select[name="triggerType"]') as HTMLSelectElement;
      const actionSelect = document.querySelector('select[name="actionType"]') as HTMLSelectElement;

      // Add event listeners
      if (triggerSelect) {
        triggerSelect.addEventListener('change', updateTriggerConditions);
        updateTriggerConditions(); // Initialize
      }

      if (actionSelect) {
        actionSelect.addEventListener('change', updateActionConfigs);
        updateActionConfigs(); // Initialize
      }

      // Setup autocomplete for user fields
      const autocompleteFields = document.querySelectorAll('.user-autocomplete input') as NodeListOf<HTMLInputElement>;
      autocompleteFields.forEach((input) => {
        const field = input.closest('.user-autocomplete') as HTMLElement;
        const type = field.dataset.autocompleteType;

        if (type) {
          // Debounced input handler
          const handleInput = debounce(async () => {
            const query = input.value.split(',').pop()?.trim() || '';
            if (query.length < 2) return;

            const suggestions = await fetchUserSuggestions(query, type);
            showSuggestions(input, suggestions, type);
          }, 300);

          input.addEventListener('input', handleInput);
        }
      });
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', initializeForm);
    } else {
      // DOM is already loaded
      setTimeout(initializeForm, 100);
    }

    // Cleanup function
    return () => {
      const dropdown = document.getElementById('autocomplete-dropdown');
      if (dropdown) dropdown.remove();
    };
  }, []);

  return null; // This component only handles side effects
}

export function AutomationFormClient() {
  return (
    <AutomationErrorBoundary>
      <AutomationFormClientInner />
    </AutomationErrorBoundary>
  );
}
