<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automation Debugging Tool</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        input { padding: 8px; margin: 5px; border: 1px solid #ddd; border-radius: 3px; width: 300px; }
        label { display: block; margin-top: 10px; font-weight: bold; }
    </style>
</head>
<body>
    <h1>🧪 Automation Debugging Tool</h1>
    
    <div class="section info">
        <h2>📋 Instructions</h2>
        <ol>
            <li>Enter your Project ID and Automation ID below</li>
            <li>Click "Get Debug Info" to see project and automation data</li>
            <li>Click "Test Automation Trigger" to manually trigger automations</li>
            <li>Check the server console for detailed logs</li>
            <li>Check your email for automation notifications</li>
        </ol>
    </div>

    <div class="section">
        <h2>🔧 Configuration</h2>
        <label for="projectId">Project ID:</label>
        <input type="text" id="projectId" placeholder="Enter project ID (e.g., 67e6f2c11c7eac73087cc900)" />
        
        <label for="automationId">Automation ID (optional):</label>
        <input type="text" id="automationId" placeholder="Enter automation ID for reference" />
        
        <label for="fromStatus">From Status:</label>
        <select id="fromStatus">
            <option value="lead">Lead</option>
            <option value="prospect">Prospect</option>
            <option value="estimate">Estimate</option>
            <option value="proposal">Proposal</option>
            <option value="contract">Contract</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="canceled">Canceled</option>
        </select>
        
        <label for="toStatus">To Status:</label>
        <select id="toStatus">
            <option value="prospect" selected>Prospect</option>
            <option value="lead">Lead</option>
            <option value="estimate">Estimate</option>
            <option value="proposal">Proposal</option>
            <option value="contract">Contract</option>
            <option value="in_progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="canceled">Canceled</option>
        </select>
    </div>

    <div class="section">
        <h2>🔍 Debug Actions</h2>
        <button onclick="getDebugInfo()">Get Debug Info</button>
        <button onclick="testAutomationTrigger()">Test Automation Trigger</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        function addResult(title, content, type = 'info') {
            const results = document.getElementById('results');
            const section = document.createElement('div');
            section.className = `section ${type}`;
            section.innerHTML = `<h3>${title}</h3><pre>${content}</pre>`;
            results.appendChild(section);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function getDebugInfo() {
            const projectId = document.getElementById('projectId').value;
            if (!projectId) {
                addResult('❌ Error', 'Please enter a Project ID', 'error');
                return;
            }

            try {
                addResult('🔄 Loading...', 'Getting debug information...', 'info');
                
                const response = await fetch(`/api/automation/test?projectId=${projectId}`);
                const data = await response.json();

                if (response.ok) {
                    addResult('✅ Debug Info Retrieved', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('❌ Error', JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('❌ Network Error', error.message, 'error');
            }
        }

        async function testAutomationTrigger() {
            const projectId = document.getElementById('projectId').value;
            const fromStatus = document.getElementById('fromStatus').value;
            const toStatus = document.getElementById('toStatus').value;

            if (!projectId) {
                addResult('❌ Error', 'Please enter a Project ID', 'error');
                return;
            }

            try {
                addResult('🚀 Triggering...', `Testing automation trigger: ${fromStatus} -> ${toStatus}`, 'info');
                
                const response = await fetch('/api/automation/test', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        projectId,
                        fromStatus,
                        toStatus,
                    }),
                });

                const data = await response.json();

                if (response.ok) {
                    addResult('✅ Automation Test Complete', JSON.stringify(data, null, 2), 'success');
                } else {
                    addResult('❌ Error', JSON.stringify(data, null, 2), 'error');
                }
            } catch (error) {
                addResult('❌ Network Error', error.message, 'error');
            }
        }

        // Auto-populate with example IDs if available
        window.onload = function() {
            // You can set default values here for testing
            // document.getElementById('projectId').value = '67e6f2c11c7eac73087cc900';
            // document.getElementById('automationId').value = '6840b72401e4824c46334a4b';
        };
    </script>
</body>
</html>
