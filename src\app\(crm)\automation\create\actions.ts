'use server';

import mongoose from 'mongoose';
import { redirect } from 'next/navigation';

import { getAutomationModel } from '@/schemas';
import { ActionResult } from '@/lib/form/types';
import {
  Automation,
  AUTOMATION_ACTION_TYPE,
  AUTOMATION_TRIGGER_TYPE,
  PROJECT_STATUS,
} from '@/schemas/automations/types';
import { validateTemplate } from '@/lib/automation-templates';

import { validatePermissions } from './permissions';

export async function create(_: unknown, formData: FormData): Promise<ActionResult> {
  'use server';
  try {
    const { user, account } = await validatePermissions();

    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const triggerType = formData.get('triggerType') as string;
    const actionType = formData.get('actionType') as string;
    const isActive = formData.get('isActive') === 'on';

    if (!name) {
      return { error: 'Name is required' };
    }

    if (!triggerType) {
      return { error: 'Trigger type is required' };
    }

    if (!actionType) {
      return { error: 'Action type is required' };
    }

    // Create the base automation object
    const automationData: Pick<
      Automation,
      '_id' | 'account' | 'name' | 'description' | 'isActive' | 'trigger' | 'actions' | 'createdBy' | 'modifiedBy'
    > = {
      _id: new mongoose.Types.ObjectId(),
      account: account._id,
      name,
      description,
      isActive,
      trigger: {
        type: triggerType as AUTOMATION_TRIGGER_TYPE,
        conditions: {},
      },
      actions: [
        {
          type: actionType as AUTOMATION_ACTION_TYPE,
          config: {},
        },
      ],
      createdBy: user._id,
      modifiedBy: user._id,
    };

    // Handle project status change trigger conditions
    if (triggerType === 'project_status_changed') {
      const fromStatus = formData.get('fromStatus') as string;
      const toStatus = formData.get('toStatus') as string;
      const daysOfInactivity = parseInt((formData.get('daysOfInactivity') as string) || '0', 10);

      automationData.trigger.projectStatusChangeConditions = {
        fromStatus: fromStatus as PROJECT_STATUS,
        toStatus: toStatus as PROJECT_STATUS,
        daysOfInactivity: Number(daysOfInactivity),
      };
    }

    // Handle project status moved trigger conditions
    if (triggerType === 'project_status_moved') {
      const toStatus = formData.get('toStatus') as string;

      automationData.trigger.projectStatusMovedConditions = {
        toStatus: toStatus as PROJECT_STATUS,
      };
    }

    // Handle email action configuration
    if (actionType === 'send_email') {
      const emailSubject = formData.get('emailSubject') as string;
      const emailBody = formData.get('emailBody') as string;
      const customEmails = formData.get('customEmails') as string;

      // Validate template variables in email fields
      const subjectValidation = validateTemplate(emailSubject || '');
      const bodyValidation = validateTemplate(emailBody || '');
      const emailsValidation = validateTemplate(customEmails || '');

      if (!subjectValidation.isValid) {
        return { error: `Invalid template variables in email subject: ${subjectValidation.invalidVariables.join(', ')}` };
      }
      if (!bodyValidation.isValid) {
        return { error: `Invalid template variables in email body: ${bodyValidation.invalidVariables.join(', ')}` };
      }
      if (!emailsValidation.isValid) {
        return { error: `Invalid template variables in email addresses: ${emailsValidation.invalidVariables.join(', ')}` };
      }

      const recipients = [];
      if (customEmails) {
        const emails = customEmails.split(',').map((email) => email.trim());
        for (const email of emails) {
          if (email) {
            recipients.push({
              type: 'custom' as const,
              email,
            });
          }
        }
      }

      automationData.actions[0].emailConfig = {
        subject: emailSubject,
        body: emailBody,
        recipients,
      };

      // Clear SMS config and general config for email action
      automationData.actions[0].smsConfig = undefined;
      automationData.actions[0].config = {};
    }

    // Handle SMS action configuration
    if (actionType === 'send_sms') {
      const smsMessage = formData.get('smsMessage') as string;
      const customPhones = formData.get('customPhones') as string;

      // Validate template variables in SMS fields
      const messageValidation = validateTemplate(smsMessage || '');
      const phonesValidation = validateTemplate(customPhones || '');

      if (!messageValidation.isValid) {
        return { error: `Invalid template variables in SMS message: ${messageValidation.invalidVariables.join(', ')}` };
      }
      if (!phonesValidation.isValid) {
        return { error: `Invalid template variables in phone numbers: ${phonesValidation.invalidVariables.join(', ')}` };
      }

      const recipients = [];
      if (customPhones) {
        const phones = customPhones.split(',').map((phone) => phone.trim());
        for (const phoneNumber of phones) {
          if (phoneNumber) {
            recipients.push({
              type: 'custom' as const,
              phoneNumber,
            });
          }
        }
      }

      automationData.actions[0].smsConfig = {
        message: smsMessage,
        recipients,
      };

      // Clear email config and general config for SMS action
      automationData.actions[0].emailConfig = undefined;
      automationData.actions[0].config = {};
    }

    // Handle Email & SMS action configuration
    if (actionType === 'email_and_sms') {
      const emailSubject = formData.get('emailSubject') as string;
      const emailBody = formData.get('emailBody') as string;
      const smsMessage = formData.get('smsMessage') as string;
      const customEmails = formData.get('customEmails') as string;
      const customPhones = formData.get('customPhones') as string;

      // Validate template variables in Email & SMS fields
      const subjectValidation = validateTemplate(emailSubject || '');
      const bodyValidation = validateTemplate(emailBody || '');
      const messageValidation = validateTemplate(smsMessage || '');
      const emailsValidation = validateTemplate(customEmails || '');
      const phonesValidation = validateTemplate(customPhones || '');

      if (!subjectValidation.isValid) {
        return { error: `Invalid template variables in email subject: ${subjectValidation.invalidVariables.join(', ')}` };
      }
      if (!bodyValidation.isValid) {
        return { error: `Invalid template variables in email body: ${bodyValidation.invalidVariables.join(', ')}` };
      }
      if (!messageValidation.isValid) {
        return { error: `Invalid template variables in SMS message: ${messageValidation.invalidVariables.join(', ')}` };
      }
      if (!emailsValidation.isValid) {
        return { error: `Invalid template variables in email addresses: ${emailsValidation.invalidVariables.join(', ')}` };
      }
      if (!phonesValidation.isValid) {
        return { error: `Invalid template variables in phone numbers: ${phonesValidation.invalidVariables.join(', ')}` };
      }

      const emailRecipients = [];
      const smsRecipients = [];

      if (customEmails) {
        const emails = customEmails.split(',').map((email) => email.trim());
        for (const email of emails) {
          if (email) {
            emailRecipients.push({
              type: 'custom' as const,
              email,
            });
          }
        }
      }

      if (customPhones) {
        const phones = customPhones.split(',').map((phone) => phone.trim());
        for (const phoneNumber of phones) {
          if (phoneNumber) {
            smsRecipients.push({
              type: 'custom' as const,
              phoneNumber,
            });
          }
        }
      }

      automationData.actions[0].emailConfig = {
        subject: emailSubject,
        body: emailBody,
        recipients: emailRecipients,
      };

      automationData.actions[0].smsConfig = {
        message: smsMessage,
        recipients: smsRecipients,
      };
    }

    // Handle In App Notification action configuration
    if (actionType === 'in_app_notification') {
      const notificationTitle = formData.get('notificationTitle') as string;
      const notificationMessage = formData.get('notificationMessage') as string;
      const notificationRecipientType = formData.get('notificationRecipientType') as string;
      const customUserIds = formData.get('customUserIds') as string;

      const config: Record<string, unknown> = {
        title: notificationTitle,
        message: notificationMessage,
        recipientType: notificationRecipientType,
      };

      if (notificationRecipientType === 'custom' && customUserIds) {
        const userIds = customUserIds.split(',').map((id) => id.trim()).filter(Boolean);
        config.userIds = userIds;
      }

      automationData.actions[0].config = config;
    }

    const automationModel = await getAutomationModel();
    const automation = new automationModel(automationData);

    await automation.save();
  } catch (error) {
    if (error instanceof Error) {
      return { error: error.message };
    }
    return { error: 'An unknown error occurred' };
  }

  // Redirect outside of try-catch to avoid NEXT_REDIRECT error
  redirect('/automation');
}
