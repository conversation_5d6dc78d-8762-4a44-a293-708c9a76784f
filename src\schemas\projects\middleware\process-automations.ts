import { Schema } from 'mongoose';
import { AutomationEngine } from '@/lib/automation-engine';
import type { Project, ProjectModel } from '@/schemas/projects/types';
import type { PROJECT_STATUS } from '@/schemas/automations/types';

/**
 * Middleware to process automations when project status changes
 */
export function processAutomations(schema: Schema<Project, ProjectModel>) {
  schema.post('save', async function () {
    console.log(`🔄 Project middleware: Processing project ${this._id}`);

    // Skip if this is a new project
    if (this.isNew) {
      console.log(`⏭️ Skipping new project ${this._id}`);
      return;
    }

    try {
      // Get the previous document state to detect status changes
      const previousDoc = this.$locals.previousDoc;

      if (!previousDoc) {
        console.log(`⚠️ No previous document found for project ${this._id}`);
        return;
      }

      // Check if project status has changed
      const previousStatus = getProjectStatus(previousDoc);
      const currentStatus = getProjectStatus(this);

      console.log(`📊 Status check for project ${this._id}: ${previousStatus} -> ${currentStatus}`);

      if (previousStatus !== currentStatus) {
        console.log(`✅ Status changed! Processing automations...`);

        // Populate necessary documents for automation processing
        await this.populate([
          { path: 'customer', select: 'firstName lastName name email emailAddresses phone phoneNumbers' },
          { path: 'assignedTo', select: 'firstName lastName name email' },
          { path: 'account', select: 'name' },
        ]);

        console.log(`📋 Project populated with customer and assignedTo data`);

        // Process automations for this status change
        await AutomationEngine.processProjectStatusChange(
          this,
          previousStatus as PROJECT_STATUS,
          currentStatus as PROJECT_STATUS
        );
      } else {
        console.log(`⏭️ No status change detected for project ${this._id}`);
      }
    } catch (error) {
      // Log error but don't throw to avoid disrupting the save operation
      console.error('❌ Error processing automations for project:', this._id, error);
    }
  });

  // Store previous document state before save for comparison
  schema.pre('save', function () {
    if (!this.isNew) {
      console.log(`💾 Storing previous state for project ${this._id}`);
      // Store the previous state for comparison in post-save
      this.$locals.previousDoc = this.toObject();
    }
  });
}

/**
 * Determine project status based on project properties
 * This maps the project's boolean flags to the PROJECT_STATUS enum
 */
function getProjectStatus(project: any): string {
  // Map project boolean flags to status
  if (project.isCancelled) return 'canceled';
  if (project.isClosed) return 'completed';
  if (project.isLead) return 'lead';
  if (project.isProspect) return 'prospect';
  
  // Check milestone-based status
  switch (project.currentMilestone) {
    case 'estimate':
      return 'estimate';
    case 'proposal':
      return 'proposal';
    case 'contract':
      return 'contract';
    case 'in_progress':
      return 'in_progress';
    case 'completed':
      return 'completed';
    default:
      // Default fallback based on boolean flags
      if (project.isLead) return 'lead';
      if (project.isProspect) return 'prospect';
      return 'lead'; // Default fallback
  }
}
