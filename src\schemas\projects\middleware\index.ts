import { Schema } from 'mongoose';

import type { Project, ProjectModel } from '@/schemas/projects/types';
import { catchAddressError } from '@/server/addresses';

import { assignMilestones } from './assign-milestones';
import { currentMilestoneFilter } from './current-milestone';
import { timestampDimensions } from './dimension-timestamps';
import { deleteFiles, fileFolderSorting, foldersEndWithSlash } from './files';
import { generateActivityHistory } from './generate-activity-history';
import { generateName } from './name';
import { notifySubscribers } from './notify-subscribers';
import { onDeletingOrders } from './order-invoice-deletion';
import { saveCustomer } from './save-customer';
import { validateMilestones } from './validate-milestones';
import { processAutomations } from './process-automations';

export function ProjectMiddleware(schema: Schema<Project, ProjectModel>) {
  saveCustomer(schema);
  assignMilestones(schema);
  generateActivityHistory(schema);
  deleteFiles(schema);
  fileFolderSorting(schema);
  foldersEndWithSlash(schema);
  timestampDimensions(schema);
  currentMilestoneFilter(schema);
  generateName(schema);
  onDeletingOrders(schema);
  notifySubscribers(schema);
  processAutomations(schema);

  schema.post('save', catchAddressError('Project validation failed: address:'));

  // Sorts comments by created date desc.
  schema.pre('save', async function () {
    if (this.comments?.length) {
      this.comments = this.comments.sort((a, z) => {
        return z.created.getTime() - a.created.getTime();
      });
    }
  });

  // Formats the adjuster phone and fax.
  schema.pre('save', async function () {
    if (this.adjuster?.phone && this.adjuster?.phone.length > 0) {
      this.adjuster.phone = this.adjuster.phone.replace(/[^0-9]/g, '');
    }
    if (this.adjuster?.fax && this.adjuster?.fax.length > 0) {
      this.adjuster.fax = this.adjuster.fax.replace(/[^0-9]/g, '');
    }
  });
}

export function ProjectValidation(schema: Schema<Project, ProjectModel>) {
  validateMilestones(schema);

  // Ensure quote name, sales, and trade.
  schema.pre('validate', async function () {
    for (const quote of this.quotes) {
      if (!quote.name.trim()) {
        throw new Error('Quote name is required');
      }
      if (!quote.sales) {
        throw new Error('Quote sales is required');
      }
      if (!quote.trade.length) {
        throw new Error('Quote trade is required');
      }
    }

    this.dimensions.forEach((dimension) => {
      const value = parseFloat(dimension.value);

      if (!dimension.name.trim()) {
        throw new Error('Dimension name is required');
      }

      if (value <= 0 || isNaN(value)) {
        throw new Error('Dimension value must be greater than 0');
      }
      if (!dimension.unit) {
        throw new Error('Dimension unit is required');
      }
    });
  });
}
