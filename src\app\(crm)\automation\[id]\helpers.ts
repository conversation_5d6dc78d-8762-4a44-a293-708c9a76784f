import { cache } from 'react';
import { notFound } from 'next/navigation';
import mongoose from 'mongoose';

import { getAutomationModel } from '@/schemas';

import { validatePermissions } from './permissions';

export const getData = cache(async function (id: string) {
  'use server';

  if (!mongoose.Types.ObjectId.isValid(id)) {
    notFound();
  }

  const automationModel = await getAutomationModel();
  const automation = await automationModel.findOne({ _id: new mongoose.Types.ObjectId(id) });

  if (!automation) {
    notFound();
  }

  // Populate related documents using Mongoose populate
  await automation.populate([
    { path: 'documents.createdBy', select: 'firstName lastName email' },
    { path: 'documents.modifiedBy', select: 'firstName lastName email' },
    { path: 'documents.account', select: 'name' },
  ]);

  const permissions = await validatePermissions(automation);

  return { ...permissions, automation };
});
